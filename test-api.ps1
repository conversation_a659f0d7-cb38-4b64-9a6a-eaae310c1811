# Test the custom API endpoint
$headers = @{
    "Authorization" = "Bearer ki2api-key-2024"
    "Content-Type" = "application/json"
}

$body = @{
    model = "claude-sonnet-4-20250514"
    messages = @(
        @{
            role = "user"
            content = "Hello! Can you respond with a simple greeting?"
        }
    )
    max_tokens = 100
} | ConvertTo-Json -Depth 3

Write-Host "Testing chat completion with custom endpoint..."
Write-Host "Endpoint: http://localhost:8989/v1/chat/completions"
Write-Host "Model: claude-sonnet-4-20250514"
Write-Host "API Key: ki2api-key-2024"
Write-Host ""

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8989/v1/chat/completions" -Method POST -Headers $headers -Body $body
    Write-Host "Success! Response:"
    Write-Host $response.Content
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
