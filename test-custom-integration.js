// Test the custom endpoint integration similar to how the extension would use it
const { createOpenAI } = require('@ai-sdk/openai');

async function testCustomEndpointIntegration() {
    console.log('Testing Custom Endpoint Integration...\n');
    
    // Configuration (same as what the extension would use)
    const customKey = 'ki2api-key-2024';
    const customEndpoint = 'http://localhost:8989/v1';
    const customModel = 'claude-sonnet-4-20250514';
    
    console.log('Configuration:');
    console.log(`- API Key: ${customKey.substring(0, 12)}...`);
    console.log(`- Endpoint: ${customEndpoint}`);
    console.log(`- Model: ${customModel}\n`);
    
    try {
        // Create OpenAI client with custom endpoint (same as extension)
        console.log('Creating OpenAI client with custom endpoint...');
        const customOpenAI = createOpenAI({
            apiKey: customKey,
            baseURL: customEndpoint
        });
        
        const model = customOpenAI(customModel);
        console.log('✓ Client created successfully\n');
        
        // Test a simple completion
        console.log('Testing completion...');
        const { streamText } = require('ai');
        
        const result = await streamText({
            model: model,
            messages: [
                {
                    role: 'user',
                    content: 'Hello! Please respond with a simple greeting to test the connection.'
                }
            ],
            maxTokens: 50
        });
        
        console.log('✓ Completion request successful');
        
        // Collect the response
        let fullResponse = '';
        for await (const chunk of result.textStream) {
            fullResponse += chunk;
        }
        
        console.log('\nResponse from custom endpoint:');
        console.log(`"${fullResponse}"`);
        
        console.log('\n✅ Custom endpoint integration test PASSED!');
        console.log('The extension should work correctly with the custom endpoint.');
        
    } catch (error) {
        console.log('\n❌ Custom endpoint integration test FAILED!');
        console.log('Error:', error.message);
        
        if (error.message.includes('ECONNREFUSED')) {
            console.log('\nTroubleshooting:');
            console.log('- Make sure the server is running on http://localhost:8989');
            console.log('- Check if the endpoint is accessible');
        } else if (error.message.includes('401') || error.message.includes('unauthorized')) {
            console.log('\nTroubleshooting:');
            console.log('- Check if the API key "ki2api-key-2024" is correct');
            console.log('- Verify the server accepts this API key');
        }
    }
}

// Run the test
testCustomEndpointIntegration().catch(console.error);
