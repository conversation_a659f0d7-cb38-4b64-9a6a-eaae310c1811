// Test script to verify the custom endpoint configuration
const vscode = require('vscode');

async function testCustomEndpointConfig() {
    console.log('Testing custom endpoint configuration...');
    
    const config = vscode.workspace.getConfiguration('superdesign');
    
    // Set the custom configuration
    await config.update('customApiKey', 'ki2api-key-2024', vscode.ConfigurationTarget.Global);
    await config.update('customEndpoint', 'http://localhost:8989/v1', vscode.ConfigurationTarget.Global);
    await config.update('customModel', 'claude-sonnet-4-20250514', vscode.ConfigurationTarget.Global);
    await config.update('aiModelProvider', 'custom', vscode.ConfigurationTarget.Global);
    
    console.log('Configuration set successfully!');
    
    // Verify the configuration
    const apiKey = config.get('customApiKey');
    const endpoint = config.get('customEndpoint');
    const model = config.get('customModel');
    const provider = config.get('aiModelProvider');
    
    console.log('Current configuration:');
    console.log('- API Key:', apiKey ? `${apiKey.substring(0, 8)}...` : 'Not set');
    console.log('- Endpoint:', endpoint);
    console.log('- Model:', model);
    console.log('- Provider:', provider);
}

// Export for use in extension
module.exports = { testCustomEndpointConfig };
