// Simple test to verify the extension configuration
const fs = require('fs');
const path = require('path');

console.log('Testing Superdesign Extension Configuration...\n');

// Test 1: Check package.json configuration
console.log('1. Checking package.json configuration...');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const config = packageJson.contributes.configuration.properties;
    
    // Check if custom endpoint properties exist
    const hasCustomApiKey = !!config['superdesign.customApiKey'];
    const hasCustomEndpoint = !!config['superdesign.customEndpoint'];
    const hasCustomModel = !!config['superdesign.customModel'];
    const hasCustomProvider = config['superdesign.aiModelProvider'].enum.includes('custom');
    const isCustomDefault = config['superdesign.aiModelProvider'].default === 'custom';
    
    console.log('   ✓ Custom API Key property:', hasCustomApiKey ? 'FOUND' : 'MISSING');
    console.log('   ✓ Custom Endpoint property:', hasCustomEndpoint ? 'FOUND' : 'MISSING');
    console.log('   ✓ Custom Model property:', hasCustomModel ? 'FOUND' : 'MISSING');
    console.log('   ✓ Custom provider in enum:', hasCustomProvider ? 'FOUND' : 'MISSING');
    console.log('   ✓ Custom as default provider:', isCustomDefault ? 'YES' : 'NO');
    
    // Check default values
    console.log('   ✓ Default endpoint:', config['superdesign.customEndpoint'].default);
    console.log('   ✓ Default model:', config['superdesign.customModel'].default);
    
    // Check if custom endpoint command exists
    const hasCustomCommand = packageJson.contributes.commands.some(cmd => 
        cmd.command === 'superdesign.configureCustomEndpoint'
    );
    console.log('   ✓ Custom endpoint command:', hasCustomCommand ? 'FOUND' : 'MISSING');
    
} catch (error) {
    console.log('   ✗ Error reading package.json:', error.message);
}

// Test 2: Check extension.ts for custom endpoint function
console.log('\n2. Checking extension.ts for custom endpoint support...');
try {
    const extensionTs = fs.readFileSync('src/extension.ts', 'utf8');
    
    const hasConfigureFunction = extensionTs.includes('configureCustomEndpoint');
    const hasCommandRegistration = extensionTs.includes('superdesign.configureCustomEndpoint');
    const hasSubscription = extensionTs.includes('configureCustomEndpointDisposable');
    
    console.log('   ✓ Configure function:', hasConfigureFunction ? 'FOUND' : 'MISSING');
    console.log('   ✓ Command registration:', hasCommandRegistration ? 'FOUND' : 'MISSING');
    console.log('   ✓ Subscription:', hasSubscription ? 'FOUND' : 'MISSING');
    
} catch (error) {
    console.log('   ✗ Error reading extension.ts:', error.message);
}

// Test 3: Check customAgentService.ts for custom provider support
console.log('\n3. Checking customAgentService.ts for custom provider support...');
try {
    const serviceTs = fs.readFileSync('src/services/customAgentService.ts', 'utf8');
    
    const hasCustomCase = serviceTs.includes("case 'custom':");
    const hasCustomConfig = serviceTs.includes('customApiKey') && serviceTs.includes('customEndpoint');
    const hasCustomInHasApiKey = serviceTs.includes("case 'custom':") && serviceTs.includes('customApiKey');
    
    console.log('   ✓ Custom case in getModel:', hasCustomCase ? 'FOUND' : 'MISSING');
    console.log('   ✓ Custom configuration reading:', hasCustomConfig ? 'FOUND' : 'MISSING');
    console.log('   ✓ Custom in hasApiKey method:', hasCustomInHasApiKey ? 'FOUND' : 'MISSING');
    
} catch (error) {
    console.log('   ✗ Error reading customAgentService.ts:', error.message);
}

// Test 4: Check ModelSelector.tsx for custom model
console.log('\n4. Checking ModelSelector.tsx for custom model...');
try {
    const modelSelectorTsx = fs.readFileSync('src/webview/components/Chat/ModelSelector.tsx', 'utf8');
    
    const hasCustomModel = modelSelectorTsx.includes('claude-sonnet-4-20250514');
    const hasCustomProvider = modelSelectorTsx.includes("provider: 'Custom'");
    
    console.log('   ✓ Custom model in list:', hasCustomModel ? 'FOUND' : 'MISSING');
    console.log('   ✓ Custom provider label:', hasCustomProvider ? 'FOUND' : 'MISSING');
    
} catch (error) {
    console.log('   ✗ Error reading ModelSelector.tsx:', error.message);
}

console.log('\n✅ Configuration test completed!');
console.log('\nTo test the functionality:');
console.log('1. Open VS Code with the extension');
console.log('2. Run "Configure Custom Endpoint" command');
console.log('3. Set API key: ki2api-key-2024');
console.log('4. Set endpoint: http://localhost:8989/v1');
console.log('5. Set model: claude-sonnet-4-20250514');
console.log('6. Test the chat functionality');
